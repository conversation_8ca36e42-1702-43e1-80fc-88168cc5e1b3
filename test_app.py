#!/usr/bin/env python3
"""
Test script to verify the Next Order Prediction System is working properly
"""

import sys
import os
import traceback
import pandas as pd

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    try:
        from flask import Flask, request, render_template, jsonify
        print("✓ Flask imports successful")
        
        import database
        print("✓ Database module imported")
        
        from next_order.data_handling.extract_data import extract_dataset
        print("✓ Extract data module imported")
        
        from next_order.data_handling.clean_data import clean_dataset
        print("✓ Clean data module imported")
        
        from next_order.prediction_and_output.prediction import prediction
        print("✓ Prediction module imported")
        
        import pandas as pd
        print("✓ Pandas imported")
        
        return True
    except Exception as e:
        print(f"✗ Import error: {e}")
        traceback.print_exc()
        return False

def test_database_connection():
    """Test database connectivity"""
    print("\nTesting database connection...")
    try:
        import database
        conn = database.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        conn.close()
        print("✓ Database connection successful")
        return True
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        return False

def test_data_file():
    """Test if the data file exists and is readable"""
    print("\nTesting data file...")
    data_path = "E:/DASUNI/CAREER PATH/Evision Micro Systems/Vs code projects/ranith_data_logs/nadun.csv"
    
    try:
        if not os.path.exists(data_path):
            print(f"✗ Data file not found: {data_path}")
            return False
        
        df = pd.read_csv(data_path)
        print(f"✓ Data file loaded successfully")
        print(f"  - Shape: {df.shape}")
        print(f"  - Columns: {list(df.columns)}")
        
        # Check required columns
        required_cols = ['date', 'customercode', 'productcode', 'productname', 'qty', 'repcode']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            print(f"✗ Missing required columns: {missing_cols}")
            return False
        
        print("✓ All required columns present")
        
        # Check for sample rep codes
        rep_codes = df['repcode'].unique()
        print(f"  - Available rep codes: {rep_codes[:10]}...")  # Show first 10
        
        return True
    except Exception as e:
        print(f"✗ Data file error: {e}")
        return False

def test_flask_app():
    """Test if Flask app can be created"""
    print("\nTesting Flask app creation...")
    try:
        from no_app import app
        print("✓ Flask app created successfully")
        
        # Test if routes are registered
        routes = [rule.rule for rule in app.url_map.iter_rules()]
        print(f"✓ Routes registered: {routes}")
        
        return True
    except Exception as e:
        print(f"✗ Flask app creation failed: {e}")
        traceback.print_exc()
        return False

def test_data_processing():
    """Test a small sample of the data processing pipeline"""
    print("\nTesting data processing pipeline...")
    try:
        from next_order.data_handling.extract_data import extract_dataset
        from next_order.data_handling.clean_data import clean_dataset
        from next_order.prediction_and_output.prediction import prediction
        
        # Load sample data
        data_path = "E:/DASUNI/CAREER PATH/Evision Micro Systems/Vs code projects/ranith_data_logs/nadun.csv"
        df = pd.read_csv(data_path)
        
        # Get first rep and outlet for testing
        first_rep = str(df['repcode'].iloc[0])
        rep_data = df[df['repcode'].astype(str) == first_rep]
        first_outlet = rep_data['customercode'].iloc[0]
        outlet_data = rep_data[rep_data['customercode'] == first_outlet]
        
        print(f"  - Testing with Rep: {first_rep}, Outlet: {first_outlet}")
        print(f"  - Sample data shape: {outlet_data.shape}")
        
        # Test extraction
        extracted_data = extract_dataset(outlet_data, first_outlet)
        print(f"✓ Data extraction successful - Shape: {extracted_data.shape}")
        
        # Test cleaning
        cleaned_data, ProductIDs, next_visit_date, outlier_product_ids = clean_dataset(extracted_data, 7)
        print(f"✓ Data cleaning successful - Products: {len(ProductIDs)}")
        
        # Test prediction (if there are products)
        if len(ProductIDs) > 0:
            predicted_df = prediction(ProductIDs, cleaned_data, next_visit_date, 7)
            print(f"✓ Prediction successful - Predictions: {len(predicted_df)}")
        else:
            print("! No products found for prediction (this may be normal for small samples)")
        
        return True
    except Exception as e:
        print(f"✗ Data processing failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("NEXT ORDER PREDICTION SYSTEM - HEALTH CHECK")
    print("=" * 60)
    
    tests = [
        ("Module Imports", test_imports),
        ("Database Connection", test_database_connection),
        ("Data File", test_data_file),
        ("Flask App", test_flask_app),
        ("Data Processing", test_data_processing)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:.<40} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! The system appears to be working properly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
