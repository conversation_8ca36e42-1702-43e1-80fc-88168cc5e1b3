from flask import Flask, request, render_template, jsonify, abort, url_for
import pandas as pd
import os
import traceback

# Import custom modules
try:
    from next_order.data_handling.extract_data import extract_dataset
    from next_order.data_handling.clean_data import clean_dataset
    from next_order.prediction_and_output.prediction import prediction
    from next_order.prediction_and_output.output import output
    import database
except ImportError as e:
    print(f"Import error: {e}")
    traceback.print_exc()

# Initialize Flask app
app = Flask(__name__,
           template_folder='templates',
           static_folder='static')

# Global variables
visit_freq = 7
# data_path="E:/DASUNI/CAREER PATH/Evision Micro Systems/Vs code projects/ranith_data_logs/ranith.csv"
data_path="E:/DASUNI/CAREER PATH/Evision Micro Systems/Vs code projects/ranith_data_logs/nadun.csv"
@app.route('/')
def read_root():
    return render_template('index.html')

# Processes a dataset and saves the predictions into a MySQL database.
@app.route('/process-dataset', methods=['POST'])
def process_data_and_save_result_in_database():
    try:
        # Require repid parameter
        repid = request.args.get('repid', None)
        if not repid:
            return jsonify({"status": "error", "message": "Rep ID (repid) is required."}), 400
        csv_data = data_path
        print(f"[DEBUG] Reading CSV file: {csv_data}")
        # Check if file exists
        if not os.path.exists(csv_data):
            return jsonify({"status": "error", "message": f"CSV file not found: {csv_data}"}), 404
        full_data = pd.read_csv(csv_data)
        print(f"[DEBUG] Unique repids in dataset: {full_data['repcode'].astype(str).unique().tolist()}")
        # Validate repid in dataset
        if 'repcode' not in full_data.columns:
            return jsonify({"status": "error", "message": "Dataset does not contain a 'repcode' column."}), 400
        if repid not in full_data['repcode'].astype(str).unique():
            print(f"[DEBUG] Rep ID {repid} not found in dataset repcode column.")
            return jsonify({"status": "error", "message": f"Rep ID {repid} not found in dataset.please check your REP ID"}), 400
        # Filter data for the given repid
        full_data = full_data[full_data['repcode'].astype(str) == str(repid)]
        db = database.get_connection()
        cursor = db.cursor()
        outlet_ids = list(full_data['customercode'].unique())
        for outlet_id, outlet_data in full_data.groupby("customercode"):
            predicted_data = []
            data = outlet_data
            extracted_dataset = extract_dataset(data, outlet_id)
            cleaned_data, ProductIDs, next_visit_date, outlier_product_ids = clean_dataset(extracted_dataset, visit_freq)
            predicted_df = prediction(ProductIDs, cleaned_data, next_visit_date, visit_freq)
            print(f"Predicted DataFrame columns: {predicted_df.columns}")
            print(f"Predicted DataFrame shape: {predicted_df.shape}")
            if not predicted_df.empty:
                print(predicted_df.head())
            else:
                print("Warning: Predicted DataFrame is empty")
                continue

            for idx, row in predicted_df.iterrows():
                # Ensure Next_Visit_Date is a datetime string in MySQL format
                try:
                    next_visit_date_value = row['Next_Visit_Date'] if pd.notnull(row['Next_Visit_Date']) else None
                except KeyError:
                    print(f"Warning: 'Next_Visit_Date' column not found in row {idx}. Available columns: {row.index.tolist()}")
                    continue
                if next_visit_date_value is not None:
                    if not isinstance(next_visit_date_value, pd.Timestamp):
                        try:
                            next_visit_date_dt = pd.to_datetime(next_visit_date_value)
                        except Exception:
                            next_visit_date_dt = None
                    else:
                        next_visit_date_dt = next_visit_date_value
                else:
                    next_visit_date_dt = None
                # Convert to MySQL DATETIME string if not None
                if next_visit_date_dt is not None:
                    next_visit_date_str = next_visit_date_dt.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    next_visit_date_str = None
                # Check for existing entry with same repid, outletid, product, and next_visit_date
                cursor.execute(
                    "SELECT 1 FROM suggested_order WHERE repid = %s AND outletid = %s AND product = %s AND Next_Visit_Date = %s LIMIT 1",
                    (repid, outlet_id, row['ProductID'], next_visit_date_str)
                )
                exists = cursor.fetchone()
                if not exists:
                    predicted_data.append((repid, outlet_id, row['ProductID'], row['predicted_qty'], next_visit_date_str))
            if predicted_data:
                cursor.executemany(
                    "INSERT INTO suggested_order(repid,outletid,product,Qty,Next_Visit_Date) VALUES (%s, %s, %s, %s, %s)",
                    predicted_data
                )
                db.commit()
        db.close()
        return jsonify({"status": "success", "message": "Data processed and saved successfully!", "outlet_ids": outlet_ids})
    except Exception as e:
        print(f"Error in process_dataset: {e}")
        traceback.print_exc()
        return jsonify({"status": "error", "message": f"An error occurred: {str(e)}"}), 500

@app.route('/get-outlet-ids', methods=['GET'])
def get_outlet_ids():
    try:
        repid = request.args.get('repid', None)
        if not repid:
            return jsonify({"status": "error", "message": "Rep ID (repid) is required."}), 400
        db = database.get_connection()
        cursor = db.cursor()
        # Try to get both outletid and outlet name if available
        try:
            cursor.execute("SELECT DISTINCT outletid, customername FROM suggested_order WHERE repid = %s ORDER BY outletid", (repid,))
            result = cursor.fetchall()
            outlet_list = [{"id": row[0], "name": row[1]} for row in result]
        except Exception:
            # Fallback: only outletid, but still filter by repid
            cursor.execute("SELECT DISTINCT outletid FROM suggested_order WHERE repid = %s ORDER BY outletid", (repid,))
            result = cursor.fetchall()
            outlet_list = [{"id": row[0], "name": str(row[0])} for row in result]
        db.close()
        return jsonify({"status": "success", "outlets": outlet_list})
    except Exception as e:
        print(f"Error in get_outlet_ids: {e}")
        traceback.print_exc()
        return jsonify({"status": "error", "message": f"An error occurred: {str(e)}"}), 500

@app.route('/output/<outletid>', methods=['POST'])
def get_output(outletid):
    try:
        db = database.get_connection()
        cursor = db.cursor()
        cursor.execute("SELECT product, Qty, Next_Visit_Date FROM suggested_order WHERE outletid = %s", (outletid,))
        result = cursor.fetchall()
        db.close()
        return jsonify({"status": "success", "data": result})
    except Exception as e:
        print(f"Error in get_output: {e}")
        traceback.print_exc()
        return jsonify({"status": "error", "message": f"An error occurred: {str(e)}"}), 500

@app.route('/get-next-visit-date/<outletid>', methods=['GET'])
def get_next_visit_date(outletid):
    try:
        db = database.get_connection()
        cursor = db.cursor()
        cursor.execute("SELECT DISTINCT Next_Visit_Date FROM suggested_order WHERE outletid = %s AND Next_Visit_Date IS NOT NULL ORDER BY Next_Visit_Date DESC LIMIT 1", (outletid,))
        result = cursor.fetchone()
        db.close()

        if result and result[0]:
            # Format the date for display
            next_visit_date = result[0]
            if isinstance(next_visit_date, str):
                # If it's already a string, try to parse it
                try:
                    import datetime
                    next_visit_date = datetime.datetime.strptime(next_visit_date, '%Y-%m-%d %H:%M:%S')
                except:
                    pass

            # Format for display
            if hasattr(next_visit_date, 'strftime'):
                formatted_date = next_visit_date.strftime('%Y-%m-%d')
            else:
                formatted_date = str(next_visit_date)

            return jsonify({
                "status": "success",
                "next_visit_date": formatted_date,
                "raw_date": str(next_visit_date)
            })
        else:
            return jsonify({
                "status": "success",
                "next_visit_date": None,
                "message": "No next visit date found for this outlet"
            })
    except Exception as e:
        print(f"Error in get_next_visit_date: {e}")
        traceback.print_exc()
        return jsonify({"status": "error", "message": f"An error occurred: {str(e)}"}), 500

@app.route('/get-outlet-ids-for-rep', methods=['GET'])
def get_outlet_ids_for_rep():
    repid = request.args.get('repid', None)
    csv_data = data_path
    if not repid:
        return jsonify({"status": "error", "message": "Rep ID (repid) is required."}), 400
    if not os.path.exists(csv_data):
        return jsonify({"status": "error", "message": f"CSV file not found: {csv_data}"}), 404
    full_data = pd.read_csv(csv_data)
    if 'repcode' not in full_data.columns or 'customercode' not in full_data.columns:
        return jsonify({"status": "error", "message": "Dataset missing required columns."}), 400
    filtered = full_data[full_data['repcode'].astype(str) == str(repid)]
    outlet_ids = sorted(filtered['customercode'].unique().tolist())
    return jsonify({"status": "success", "outlet_ids": outlet_ids})

# @app.route('/remove-duplicates', methods=['POST'])
# def remove_duplicates():
#     try:
#         db = database.get_connection()
#         cursor = db.cursor()
#         # Delete duplicates, keeping the row with the lowest id (assuming there is an id column, otherwise use all columns in the WHERE)
#         cursor.execute('''
#             DELETE t1 FROM suggested_order t1
#             INNER JOIN suggested_order t2
#             WHERE
#                 t1.id > t2.id AND
#                 t1.repid = t2.repid AND
#                 t1.outletid = t2.outletid AND
#                 t1.product = t2.product AND
#                 (
#                     (t1.Next_Visit_Date IS NULL AND t2.Next_Visit_Date IS NULL) OR
#                     (t1.Next_Visit_Date = t2.Next_Visit_Date)
#                 )
#         ''')
#         db.commit()
#         db.close()
#         return jsonify({"status": "success", "message": "Duplicate rows removed from suggested_order table."})
#     except Exception as e:
#         print(f"Error in remove_duplicates: {e}")
#         traceback.print_exc()
#         return jsonify({"status": "error", "message": f"An error occurred while removing duplicate entries: {str(e)}"}), 500


if __name__ == '__main__':
    print("Starting Flask application...")
    app.run(debug=True, host='127.0.0.1', port=5000)