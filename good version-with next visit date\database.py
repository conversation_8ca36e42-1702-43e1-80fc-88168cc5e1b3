import mysql.connector
# define a function to get connection with the database
def get_connection():

    connection =mysql.connector.connect(
        host='localhost',
        user='root',
        password='8UD-gFQY,zh9-sG',
        database='order_prediction_db'
    )
    return connection
# # cursor will execute sql commands
# cursor = connection.cursor()

# # cursor.execute('CREATE DATABASE order_prediction_db')

# query1 ="INSERT INTO suggested_order(repid,outletid,producr,Qty)"
# data1 =('1','1','A',10)
# cursor.execute(query1,data1)
# connection.commit()

# cursor.close()
# connection.close()
# print('Data inserted successfully')
