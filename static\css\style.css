/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* CSS Variables for consistent theming */
:root {
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --primary-light: #a5b4fc;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;
    --danger-color: #ef4444;
    --warning-color: #f97316;
    --success-color: #22c55e;

    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;

    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-tertiary: #f3f4f6;

    --border-color: #e5e7eb;
    --border-light: #f3f4f6;

    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

/* Main Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: var(--text-primary);
    font-weight: 400;
}

.container {
    max-width: 1200px;
    margin: 2rem auto;
    background-color: var(--bg-primary);
    padding: 2rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Header */
header {
    text-align: center;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid var(--border-light);
    position: relative;
}

header::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

h1 {
    color: var(--text-primary);
    margin: 0;
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.subtitle {
    color: var(--text-secondary);
    font-size: 1.25rem;
    margin-top: 0.5rem;
    font-weight: 400;
}

/* Tabs */
.tab {
    overflow: hidden;
    border: 1px solid #ccc;
    background-color: #f8f9fa;
    border-radius: 5px 5px 0 0;
    margin-bottom: 0;
}

.tab button {
    background-color: inherit;
    float: left;
    border: none;
    outline: none;
    cursor: pointer;
    padding: 14px 20px;
    transition: 0.3s;
    font-size: 17px;
    color: #495057;
    font-weight: 500;
}

.tab button:hover {
    background-color: #e9ecef;
}

.tab button.active {
    background-color: #fff;
    border-bottom: 3px solid #3498db;
    color: #3498db;
}

/* Tab Content */
.tabcontent {
    display: none;
    padding: 20px;
    border: 1px solid #ccc;
    border-top: none;
    border-radius: 0 0 5px 5px;
    background-color: #fff;
    animation: fadeEffect 0.5s;
}

@keyframes fadeEffect {
    from {opacity: 0;}
    to {opacity: 1;}
}

/* Form Elements */
.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
}

input[type="text"], select {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 16px;
    transition: border-color 0.3s;
    background-color: white;
}

input[type="text"]:focus, select:focus {
    border-color: #3498db;
    outline: none;
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
}

select {
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    padding-right: 40px;
}

select option {
    padding: 8px 12px;
}

button {
    background-color: #3498db;
    color: white;
    padding: 12px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #2980b9;
}

/* Results */
.result {
    margin-top: 25px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
    display: none;
}

.result h3 {
    margin-top: 0;
    color: #2c3e50;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #f2f2f2;
    font-weight: 600;
    color: #2c3e50;
}

tr:hover {
    background-color: #f5f5f5;
}

/* Loading Spinner */
.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
    display: none;
}

/* Small spinner for outlet loading */
#outletSpinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    margin: 10px auto 0;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status Messages */
.status-message {
    padding: 15px;
    margin: 15px 0;
    border-radius: 4px;
}

.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Processing Details */
.processing-details {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 5px;
    border: 1px solid #e9ecef;
    margin-top: 15px;
}

.processing-details h4 {
    margin-top: 15px;
    margin-bottom: 10px;
    color: #2c3e50;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
}

.processing-details p {
    margin: 10px 0;
}

.processing-details ul {
    margin: 10px 0;
    padding-left: 20px;
}

.processing-details li {
    margin-bottom: 5px;
}

.success-text {
    color: #28a745;
    font-weight: 600;
}

/* Next Visit Date Styles */
.next-visit-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.next-visit-info h4 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 500;
}

.next-visit-date {
    font-weight: 700;
    font-size: 1.1em;
    background-color: rgba(255, 255, 255, 0.2);
    padding: 5px 15px;
    border-radius: 20px;
    display: inline-block;
    margin-left: 10px;
}

.no-date {
    font-weight: 500;
    font-style: italic;
    opacity: 0.8;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 5px 15px;
    border-radius: 20px;
    display: inline-block;
    margin-left: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
        margin: 10px;
    }

    .tab button {
        padding: 10px 15px;
        font-size: 15px;
    }

    input[type="text"], button {
        padding: 10px;
    }
}
