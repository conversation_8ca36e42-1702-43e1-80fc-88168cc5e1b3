#!/usr/bin/env python3
"""
Test script to verify the next visit date functionality
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.getcwd())

def test_imports():
    """Test if all required modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        import flask
        print("✅ Flask imported successfully")
    except ImportError as e:
        print(f"❌ Flask import failed: {e}")
        return False
    
    try:
        import pandas as pd
        print("✅ Pandas imported successfully")
    except ImportError as e:
        print(f"❌ Pandas import failed: {e}")
        return False
    
    try:
        import database
        print("✅ Database module imported successfully")
    except ImportError as e:
        print(f"❌ Database import failed: {e}")
        return False
    
    return True

def test_database_connection():
    """Test database connection"""
    print("\n🔌 Testing database connection...")
    
    try:
        import database
        db = database.get_connection()
        cursor = db.cursor()
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        db.close()
        print("✅ Database connection successful")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_suggested_order_table():
    """Test if suggested_order table exists and has data"""
    print("\n📊 Testing suggested_order table...")
    
    try:
        import database
        db = database.get_connection()
        cursor = db.cursor()
        
        # Check if table exists
        cursor.execute("SHOW TABLES LIKE 'suggested_order'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("❌ suggested_order table does not exist")
            db.close()
            return False
        
        print("✅ suggested_order table exists")
        
        # Check table structure
        cursor.execute("DESCRIBE suggested_order")
        columns = cursor.fetchall()
        print("📋 Table structure:")
        for col in columns:
            print(f"   - {col[0]} ({col[1]})")
        
        # Check if there's data
        cursor.execute("SELECT COUNT(*) FROM suggested_order")
        count = cursor.fetchone()[0]
        print(f"📈 Total records: {count}")
        
        # Check for Next_Visit_Date column specifically
        cursor.execute("SELECT COUNT(*) FROM suggested_order WHERE Next_Visit_Date IS NOT NULL")
        date_count = cursor.fetchone()[0]
        print(f"📅 Records with Next_Visit_Date: {date_count}")
        
        # Sample data
        cursor.execute("SELECT outletid, product, Qty, Next_Visit_Date FROM suggested_order LIMIT 5")
        sample_data = cursor.fetchall()
        print("📋 Sample data:")
        for row in sample_data:
            print(f"   - Outlet: {row[0]}, Product: {row[1]}, Qty: {row[2]}, Next Visit: {row[3]}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Table test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting Next Visit Date UI Tests")
    print("=" * 50)
    
    # Test imports
    if not test_imports():
        print("\n❌ Import tests failed. Cannot proceed.")
        return False
    
    # Test database connection
    if not test_database_connection():
        print("\n❌ Database tests failed. Cannot proceed.")
        return False
    
    # Test table structure and data
    if not test_suggested_order_table():
        print("\n❌ Table tests failed. Cannot proceed.")
        return False
    
    print("\n✅ All tests passed! The application should work correctly.")
    print("\n📝 Summary of changes made:")
    print("1. ✅ Modified /output/<outletid> endpoint to include Next_Visit_Date")
    print("2. ✅ Added /get-next-visit-date/<outletid> endpoint")
    print("3. ✅ Updated JavaScript to display next visit date prominently")
    print("4. ✅ Added date formatting function")
    print("5. ✅ Enhanced CSS styling for next visit date display")
    print("6. ✅ Added Next Visit Date column to the results table")
    
    return True

if __name__ == "__main__":
    main()
